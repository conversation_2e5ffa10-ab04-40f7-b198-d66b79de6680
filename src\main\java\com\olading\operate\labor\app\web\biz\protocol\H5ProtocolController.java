package com.olading.operate.labor.app.web.biz.protocol;

import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.identity.AuthContext;
import com.olading.operate.labor.domain.service.IdentityService;
import com.olading.operate.labor.domain.service.ProtocolService;
import com.olading.operate.labor.domain.share.identity.dto.FaceAuthResult;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.user.UserManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "个人端-协议接口")
@RestController
@RequestMapping("/api/personal/protocol")
@RequiredArgsConstructor
@Slf4j
public class H5ProtocolController extends BusinessController {

    private final ProtocolService protocolService;
    private final IdentityService identityService;
    private final UserManager userManager;


    @Operation(summary = "活体人脸识别", description = "通过视频进行活体人脸识别")
    @PostMapping("/faceAuth")
    public WebApiResponse<FaceAuthResult> liveFaceAuth(@Valid @RequestBody H5FaceAuthRequest request) {
        Long supplierId = currentSupplierId();
        final PersonInfoData userInfo = userManager.getUserInfo(currentUserId());
        log.info("活体人脸识别请求: supplierId={}, name={}, idNo={}",
                supplierId, userInfo.getName(), userInfo.getIdCard());

        FaceAuthResult result = identityService.liveFaceAuthWithContext(
                userInfo.getName(),
                userInfo.getIdCard(),
                request.getVideoBase64(),
                AuthContext.builder()
                        .tenantId(currentTenant().toTenantId())
                        .authScene("REAL_NAME_AUTH")
                        .userId(currentUserId())
                        .supplierId(supplierId)
                        .faceVideoFileId(request.getVideoBase64())
                        .build()
        );

        return WebApiResponse.success(result);
    }

    @Operation(summary = "h5个人签署")
    @RequestMapping(value = "sign", method = RequestMethod.POST)
    public WebApiResponse<Void> sign(@RequestBody SignVo signVo) {
        TemplateController.SupplierAndUser supplierAndUser = new TemplateController.SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        protocolService.sign(signVo.getProtocolId(), signVo.getSignImage(), currentUserId(), supplierAndUser);
        return WebApiResponse.success();
    }
    @Operation(summary = "人员待签署列表查询")
    @PostMapping("/getLaborContract")
    public WebApiResponse<List<ProtocolController.LaborProtocolVo>> getLaborContract() {
        TemplateController.SupplierAndUser supplierAndUser = new TemplateController.SupplierAndUser(currentUser(), currentSupplier(), currentTenant());
        List<ProtocolController.LaborProtocolVo> laborContract = protocolService.getLaborContract(supplierAndUser);
        return WebApiResponse.success(laborContract);
    }

    @Data
    public static class SignVo {
        @Schema(description = "协议id")
        private Long protocolId;
        @Schema(description = "签名图片base64,为空则使用默认签名")
        private String signImage;
    }


    @Data
    @Schema(description = "活体人脸识别请求")
    public static class H5FaceAuthRequest {

        @NotBlank(message = "视频数据不能为空")
        @Schema(description = "视频base64", required = true)
        private String videoBase64;
    }
}
