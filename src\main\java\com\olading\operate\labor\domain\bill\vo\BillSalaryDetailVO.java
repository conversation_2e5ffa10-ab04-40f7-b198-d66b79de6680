package com.olading.operate.labor.domain.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单薪酬明细视图对象
 */
@Data
@Schema(description = "账单薪酬明细信息")
public class BillSalaryDetailVO {

    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "账单主表ID")
    private Long billMasterId;

    @Schema(description = "账单分类ID")
    private Long billCategoryId;

    @Schema(description = "薪酬明细ID")
    private Long salaryDetailId;

    @Schema(description = "薪酬批次ID")
    private Long salaryBatchId;

    @Schema(description = "人员姓名")
    private String laborName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "应发工资")
    private BigDecimal grossSalary;

    @Schema(description = "实发工资")
    private BigDecimal netSalary;

    @Schema(description = "应缴个税")
    private BigDecimal incomeTax;

    @Schema(description = "应缴增值税")
    private BigDecimal vatTax;

    @Schema(description = "应缴附加税")
    private BigDecimal additionalTax;

    @Schema(description = "账单月份")
    private LocalDate billMonth;

    @Schema(description = "工资所属期")
    private String salaryPeriod;

    @Schema(description = "备注")
    private String remark;
}