package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.boot.util.jpa.JpaUtils;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.invoice.InvoiceStatus;
import com.olading.operate.labor.domain.invoice.QInvoiceEntity;
import com.olading.operate.labor.domain.invoice.vo.InvoiceVO;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Builder;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 开票查询组件
 */
@Component
public class InvoiceQuery implements EntityQuery<QueryFilter<InvoiceQuery.Filters>, InvoiceVO> {

    private final QInvoiceEntity t1 = QInvoiceEntity.invoiceEntity;
    private final QCustomerEntity t2 = QCustomerEntity.customerEntity;
    private final QBusinessContractEntity t3 = QBusinessContractEntity.businessContractEntity;
    private final QSupplierCorporationEntity t4 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {
        BooleanBuilder criteria = new BooleanBuilder();

        // 基础条件
        if (filters.getFilters().getId() != null) {
            criteria.and(t1.id.eq(filters.getFilters().getId()));
        }

        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }

        if (filters.getFilters().getCustomerId() != null) {
            criteria.and(t1.customerId.in(filters.getFilters().getCustomerId()));
        }

        if (filters.getFilters().getContractId() != null) {
            criteria.and(t1.contractId.eq(filters.getFilters().getContractId()));
        }

        // 模糊查询
        if (StringUtils.hasText(filters.getFilters().getSn())) {
            criteria.and(t1.sn.like(JpaUtils.fullLike(filters.getFilters().getSn())));
        }

        if (StringUtils.hasText(filters.getFilters().getCustomerName())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getCustomerName())));
        }

        // 状态查询
        if (filters.getFilters().getStatus() != null) {
            criteria.and(t1.status.eq(filters.getFilters().getStatus()));
        }

        // 日期范围查询
        if (filters.getFilters().getCreateTimeStart() != null) {
            criteria.and(t1.createTime.goe(filters.getFilters().getCreateTimeStart()));
        }

        if (filters.getFilters().getCreateTimeEnd() != null) {
            criteria.and(t1.createTime.loe(filters.getFilters().getCreateTimeEnd()));
        }

        if (filters.getFilters().getContractIds() != null) {
            criteria.and(t1.contractId.in(filters.getFilters().getContractIds()));
        }

        // 默认只查询未删除的记录
        criteria.and(t1.deleted.eq(false));

        query.select(t1, t2, t3, t4)
            .from(t1)
            .leftJoin(t2).on(t1.customerId.eq(t2.id))
            .leftJoin(t3).on(t1.contractId.eq(t3.id))
                .leftJoin(t4).on(t1.supplierCorporationId.eq(t4.id))
            .where(criteria);
    }

    @Override
    public InvoiceVO transform(Object v) {
        Tuple tuple = (Tuple) v;
        var invoice = tuple.get(t1);
        var customer = tuple.get(t2);
        var contract = tuple.get(t3);
        var supplierCorporation = tuple.get(t4);

        InvoiceVO vo = new InvoiceVO();
        BeanUtils.copyProperties(invoice, vo);
        vo.setStatusDesc(invoice.getStatus().getDescription());
        vo.setTypeDesc(invoice.getType().getDescription());

        // 设置关联信息
        if (customer != null) {
            vo.setCustomerName(customer.getName());
        }
        if (contract != null) {
            vo.setContractName(contract.getName());
        }
        if (supplierCorporation != null) {
            vo.setSupplierCorporationName(supplierCorporation.getName());
        }

        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        return switch (column) {
            case "id" -> t1.id;
            case "sn" -> t1.sn;
            case "status" -> t1.status;
            case "fee" -> t1.fee;
            case "createTime" -> t1.createTime;
            default -> t1.id;
        };
    }

    @Data
    public static class Filters {
        private Long id;
        private Long supplierId;
        private Set<Long> customerId;
        private Long contractId;
        private String sn;
        private InvoiceStatus status;
        private String customerName;
        private LocalDateTime createTimeStart;
        private LocalDateTime createTimeEnd;
        private Set<Long> contractIds;
    }
}