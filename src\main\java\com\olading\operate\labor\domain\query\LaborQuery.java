package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.enums.EnumContractSignStatus;
import com.olading.operate.labor.app.web.biz.labor.SupplierLaborController;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.share.contract.QBusinessContractEntity;
import com.olading.operate.labor.domain.share.customer.QCustomerEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.QLaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.QSupplierLaborEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.QLaborProtocolEntity;
import com.olading.operate.labor.domain.supplier.QSupplierEntity;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

public class LaborQuery implements EntityQuery<QueryFilter<LaborQuery.Filters>, LaborQuery.Record> {

    private final QLaborInfoEntity t1 = QLaborInfoEntity.laborInfoEntity;

    private final QSupplierLaborEntity t2 = QSupplierLaborEntity.supplierLaborEntity;

    private final QCustomerEntity customer = QCustomerEntity.customerEntity;

    private final QSupplierEntity supplier = QSupplierEntity.supplierEntity;

    private final QSupplierCorporationEntity corporation = QSupplierCorporationEntity.supplierCorporationEntity;

    private final QBusinessContractEntity contract = QBusinessContractEntity.businessContractEntity;

    private final QLaborProtocolEntity protocol = QLaborProtocolEntity.laborProtocolEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<Filters> filters) {

        BooleanBuilder criteria = new BooleanBuilder();
        if (filters.getFilters().getSupplierId() != null) {
            criteria.and(t1.supplierId.eq(filters.getFilters().getSupplierId()));
        }
         if (filters.getFilters().getCustomerId() != null) {
            criteria.and(t1.customerId.eq(filters.getFilters().getCustomerId()));
        }
         if (filters.getFilters().getContractId() != null) {
            criteria.and(t1.contractId.eq(filters.getFilters().getContractId()));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getName())) {
            criteria.and(t2.name.like(JpaUtils.fullLike(filters.getFilters().getName())));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getCellPhone())) {
            criteria.and(t2.cellphone.like(JpaUtils.fullLike(filters.getFilters().cellPhone)));
        }
        if (StringUtils.isNotBlank(filters.getFilters().getIdCard())) {
            criteria.and(t2.idCard.like(JpaUtils.fullLike(filters.getFilters().idCard)));
        }
         if (StringUtils.isNotBlank(filters.getFilters().getEmpStatus())) {
            criteria.and(t1.empStatus.eq(filters.getFilters().empStatus));
        }
         if (filters.getFilters().getContractIds() != null) {
             criteria.and(t1.contractId.in(filters.getFilters().getContractIds()));
         }

        query.select(t1, t2, customer.name, corporation.name, supplier.name, contract.name, protocol)
                .distinct()
                .from(t1)
                .leftJoin(t2).on(t1.laborId.eq(t2.id))
                .leftJoin(customer).on(t1.customerId.eq(customer.id))
                .leftJoin(corporation).on(t1.supplierCorporationId.eq(corporation.id))
                .leftJoin(supplier).on(t1.supplierId.eq(supplier.id))
                .leftJoin(contract).on(t1.contractId.eq(contract.id))
                .leftJoin(protocol).on(t2.idCard.eq(protocol.idCard).and(t1.supplierCorporationId.eq(protocol.supplierCorporationId)));

        query.where(criteria);
    }

    @Override
    public LaborQuery.Record transform(Object v) {
        Tuple tuple = (Tuple) v;

        SupplierLaborEntity labor = tuple.get(t2);
        LaborInfoEntity laborInfo = tuple.get(t1);

        LaborQuery.Record record = new LaborQuery.Record();
        record.setLabor(labor);
        record.setLaborInfo(laborInfo);
        record.setCustomerName(tuple.get(customer.name));
        record.setCorporationName(tuple.get(corporation.name));
        record.setSupplierName(tuple.get(supplier.name));
        record.setContractName(tuple.get(contract.name));

        return record;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t1.id;
        }
        return null;
    }

    @Data
    public static class Filters {

        @Schema(description = "姓名")
        private String name;
        @Schema(description = "手机号")
        private String cellPhone;
        @Schema(description = "证件号码")
        private String idCard;
        @Schema(description = "所属平台")
        private Long supplierId;
        @Schema(description = "作业主体id")
        private Long corporationId;;
        @Schema(description = "所属客户")
        private Long customerId;
        @Schema(description = "所属服务合同")
        private Long contractId;
        @Schema(description = "所属服务合同列表")
        private Set<Long> contractIds;
        @Schema(description = "人员状态")
        private String empStatus;

        public static Filters builder(SupplierLaborController.SupplierLaborQueryVo param) {
            Filters filters = new Filters();
            filters.setName(param.getName());
            filters.setCellPhone(param.getCellPhone());
            filters.setIdCard(param.getIdCard());
            filters.setSupplierId(param.getSupplierId());
            filters.setCustomerId(param.getCustomerId());
            filters.setContractId(param.getContractId());
            filters.setEmpStatus(param.getEmpStatus());
            filters.setContractIds(param.getContractIds());
            return filters;
        }
    }

    @Data
    public static class Record {
        private SupplierLaborEntity labor;
        private LaborInfoEntity laborInfo;
        private LaborProtocolEntity protocol;
        private String customerName;
        private String corporationName;
        private String supplierName;
        private String contractName;
        public SupplierLaborController.LaborRecordVo toVo() {
            SupplierLaborController.LaborRecordVo laborRecordVo = new SupplierLaborController.LaborRecordVo();
            if(labor != null){
                laborRecordVo.setName(labor.getName());
                laborRecordVo.setIdCard(labor.getIdCard());
                laborRecordVo.setCellphone(labor.getCellphone());
            }
            if(laborInfo != null){
                laborRecordVo.setId(laborInfo.getId());
                laborRecordVo.setEmpStatus(laborInfo.getEmpStatus());
                laborRecordVo.setCreateTime(laborInfo.getCreateTime());
                laborRecordVo.setModifyTime(laborInfo.getModifyTime());
            }
            laborRecordVo.setSignStatus(protocol != null
                    && EnumContractSignStatus.COMPLETE.name().equals(protocol.getProtocolStatus()));

            laborRecordVo.setSupplierName(supplierName);
            laborRecordVo.setCustomerName(customerName);
            laborRecordVo.setCorporationName(corporationName);

            laborRecordVo.setContractName(contractName);
            return laborRecordVo;
        }
    }
}
