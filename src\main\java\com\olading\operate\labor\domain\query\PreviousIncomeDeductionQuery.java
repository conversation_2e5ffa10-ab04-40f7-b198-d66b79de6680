package com.olading.operate.labor.domain.query;

import com.olading.boot.util.jpa.JpaUtils;
import com.olading.boot.util.jpa.querydsl.EntityQuery;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.salary.PreviousIncomeDeductionEntity;
import com.olading.operate.labor.domain.salary.QPreviousIncomeDeductionEntity;
import com.olading.operate.labor.domain.salary.vo.PreviousIncomeDeductionVO;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Set;

public class PreviousIncomeDeductionQuery implements EntityQuery<QueryFilter<PreviousIncomeDeductionQuery.Filters>, PreviousIncomeDeductionVO> {

    private final QPreviousIncomeDeductionEntity t = QPreviousIncomeDeductionEntity.previousIncomeDeductionEntity;

    private final QSupplierCorporationEntity t2 = QSupplierCorporationEntity.supplierCorporationEntity;

    @Override
    public void select(JPAQuery<?> query, QueryFilter<PreviousIncomeDeductionQuery.Filters> filters) {
        BooleanBuilder builder = new BooleanBuilder();

        PreviousIncomeDeductionQuery.Filters f = filters.getFilters();

        if (f.getId() != null) {
            builder.and(t.id.eq(f.getId()));
        }

        if (f.getSupplierCorporationId() != null) {
            builder.and(t.supplierCorporationId.eq(f.getSupplierCorporationId()));
        }
        if (StringUtils.isNotBlank(f.getSupplierCorporationName())) {
            builder.and(t2.name.like(JpaUtils.fullLike(f.getSupplierCorporationName())));
        }
        if (!filters.isWithDeleted()) {
            builder.and(t.deleted.eq(false));
        }
        if(f.getSupplierCorporationIds() != null){
            builder.and(t.supplierCorporationId.in(f.getSupplierCorporationIds()));
        }

        query.select(t,t2)
                .from(t)
                .leftJoin(t2).on(t.supplierCorporationId.eq(t2.id))
                .where(builder);
    }

    @Override
    public PreviousIncomeDeductionVO transform(Object v) {
        Tuple tuple = (Tuple) v;
        PreviousIncomeDeductionEntity entity = tuple.get(t);
        SupplierCorporationEntity supplierCorporationEntity = tuple.get(t2);
        PreviousIncomeDeductionVO vo = new PreviousIncomeDeductionVO();
        assert entity != null;
        BeanUtils.copyProperties(entity, vo);
        if(supplierCorporationEntity != null){
            vo.setSupplierCorporationName(supplierCorporationEntity.getName());
        }
        return vo;
    }

    @Override
    public ComparableExpressionBase<?> columnMapping(String column) {
        if ("id".equals(column)) {
            return t.id;
        }
        return null;
    }

    @Data
    public static class Filters {
        private Long id;
        private Long supplierCorporationId;
        private String supplierCorporationName;
        private Set<Long> supplierCorporationIds;
    }
}
