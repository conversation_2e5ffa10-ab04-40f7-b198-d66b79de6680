package com.olading.operate.labor.domain.salary;

import com.olading.operate.labor.domain.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

@Getter
@Setter
@Comment("上期收入减除表")
@Entity
@Table(name = "t_previous_income_deduction", schema = "olading_labor")
public class PreviousIncomeDeductionEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 100)
    @NotNull
    @Comment("姓名")
    @Column(name = "full_name", nullable = false, length = 100)
    private String fullName;

    @Size(max = 18)
    @NotNull
    @Comment("身份证号")
    @Column(name = "id_number", nullable = false, length = 18)
    private String idNumber;

    @Comment("累计收入")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_income", precision = 16, scale = 2)
    private BigDecimal accumulatedIncome;

    @Comment("累计费用")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_expenses", precision = 16, scale = 2)
    private BigDecimal accumulatedExpenses;

    @Comment("累计免税收入")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_free_income", precision = 16, scale = 2)
    private BigDecimal accumulatedTaxFreeIncome;

    @Comment("累计依法确定的其他扣除")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_other_deductions", precision = 16, scale = 2)
    private BigDecimal accumulatedOtherDeductions;

    @Comment("累计已预缴税额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_prepaid_tax", precision = 16, scale = 2)
    private BigDecimal accumulatedPrepaidTax;

    @Comment("累计减免税额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_reductions", precision = 16, scale = 2)
    private BigDecimal accumulatedTaxReductions;

    @NotNull
    @Comment("累计减除费用 (月数*5000)")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_deduction_expenses", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedDeductionExpenses;

    @Size(max = 7)
    @NotNull
    @Comment("税款所属期（格式：2025-06）")
    @Column(name = "tax_period", nullable = false, length = 7)
    private String taxPeriod;

    @NotNull
    @Comment("上传时间")
    @Column(name = "upload_time", nullable = false)
    private LocalDateTime uploadTime;

    @NotNull
    @Comment("服务商ID")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

}