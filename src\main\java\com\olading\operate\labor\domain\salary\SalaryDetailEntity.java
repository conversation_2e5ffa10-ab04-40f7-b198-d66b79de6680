package com.olading.operate.labor.domain.salary;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;

@Getter
@Setter
@Comment("工资明细表")
@Entity
@Table(name = "t_salary_detail")
@AttributeOverrides({
        @AttributeOverride(name = "tenantId", column = @Column(name = "tenant_id", length = 50)),
        @AttributeOverride(name = "createTime", column = @Column(name = "create_time", nullable = false)),
        @AttributeOverride(name = "modifyTime", column = @Column(name = "modify_time", nullable = false))
})
public class SalaryDetailEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("员工工资明细ID")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("工资表ID")
    @Column(name = "salary_statement_id", nullable = false)
    private Long salaryStatementId;

    @Size(max = 100)
    @NotNull
    @Comment("姓名")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 18)
    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 18)
    private String idCard;

    @Size(max = 20)
    @Comment("手机号")
    @Column(name = "phone_number", nullable = false, length = 20)
    private String phoneNumber;

    @Size(max = 7)
    @NotNull
    @Comment("税款所属期 (YYYY-MM)")
    @Column(name = "tax_period", nullable = false, length = 7)
    private String taxPeriod;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @NotNull
    @Comment("本次应发金额")
    @ColumnDefault("0.00")
    @Column(name = "payable_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal payableAmount;

    @NotNull
    @Comment("本次免税收入")
    @ColumnDefault("0.00")
    @Column(name = "tax_free_income", nullable = false, precision = 16, scale = 2)
    private BigDecimal taxFreeIncome;

    @NotNull
    @Comment("本次依法确定的其他扣除")
    @ColumnDefault("0.00")
    @Column(name = "other_deductions", nullable = false, precision = 16, scale = 2)
    private BigDecimal otherDeductions;

    @NotNull
    @Comment("本次减免税额")
    @ColumnDefault("0.00")
    @Column(name = "tax_relief_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal taxReliefAmount;

    @NotNull
    @Comment("累计收入")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_income", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedIncome;

    @NotNull
    @Comment("累计费用（累计收入*20%）")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_expenses", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedExpenses;

    @NotNull
    @Comment("累计免税收入 (累加)")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_free_income", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedTaxFreeIncome;

    @NotNull
    @Comment("累计依法确定的其他扣除 (累加)")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_other_deductions", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedOtherDeductions;

    @NotNull
    @Comment("累计减免税额 (累加)")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_relief", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedTaxRelief;

    @NotNull
    @Comment("累计应纳税所得额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_taxable_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedTaxableAmount;

    @NotNull
    @Comment("累计应纳税额")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_tax_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedTaxAmount;

    @NotNull
    @Comment("累计已预缴税额,累计已报税的收入")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_prepaid_tax", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedPrepaidTax;


    @NotNull
    @Comment("本期应预扣预缴税额（当前月份累计）")
    @ColumnDefault("0.00")
    @Column(name = "current_tax_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal currentTaxAmount;

    @NotNull
    @Comment("本次应预扣预缴税额")
    @ColumnDefault("0.00")
    @Column(name = "current_withholding_tax", nullable = false, precision = 16, scale = 2)
    private BigDecimal currentWithholdingTax;

    @NotNull
    @Comment("实发金额")
    @ColumnDefault("0.00")
    @Column(name = "net_payment", nullable = false, precision = 16, scale = 2)
    private BigDecimal netPayment;

    @NotNull
    @Comment("增值税额")
    @ColumnDefault("0.00")
    @Column(name = "vat_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal vatAmount;

    @NotNull
    @Comment("增值附加税额")
    @ColumnDefault("0.00")
    @Column(name = "additional_tax_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal additionalTaxAmount;

    @NotNull
    @Comment("城市维护建设附加税")
    @ColumnDefault("0.00")
    @Column(name = "urban_construction_tax", nullable = false, precision = 16, scale = 2)
    private BigDecimal urbanConstructionTax;

    @NotNull
    @Comment("教育费附加税")
    @ColumnDefault("0.00")
    @Column(name = "education_surcharge", nullable = false, precision = 16, scale = 2)
    private BigDecimal educationSurcharge;

    @NotNull
    @Comment("地方教育附加税")
    @ColumnDefault("0.00")
    @Column(name = "local_education_surcharge", nullable = false, precision = 16, scale = 2)
    private BigDecimal localEducationSurcharge;

    @NotNull
    @Comment("是否删除 (0=否, 1=是)")
    @ColumnDefault("0")
    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @NotNull
    @Comment("累计减除费用 (月数*5000)")
    @ColumnDefault("0.00")
    @Column(name = "accumulated_deduction_expenses", nullable = false, precision = 16, scale = 2)
    private BigDecimal accumulatedDeductionExpenses;

    public SalaryDetailEntity(TenantInfo info){
        setTenant(info);
    }

    public SalaryDetailEntity() {
    }

}