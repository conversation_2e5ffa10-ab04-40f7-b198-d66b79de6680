package com.olading.operate.labor.domain.salary;


import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
@Transactional
public class SalaryManager {

    private final EntityManager em;
    private final CorporationManager corporationManager;

    public SalaryStatementEntity querySalaryStatement(Long id) {
        return em.find(SalaryStatementEntity.class, id);
    }

    /**
     * 根据税款所属期+作业主体查询工资表
     * */
    public List<SalaryStatementEntity> querySalaryStatement(List<String> taxPeriodList, Long supplierCorporationId) {
        return querySalaryStatement(t -> t.taxPeriod.in(taxPeriodList).and(t.supplierCorporationId.eq(supplierCorporationId))).fetch();
    }

    /**
     * 根据作业主体查询是否存在未确认工资表
     * */
    public List<SalaryStatementEntity> queryUnconfirmedSalaryStatement(Long supplierCorporationId) {
        return querySalaryStatement(t -> t.supplierCorporationId.eq(supplierCorporationId).and(t.status.ne(SalaryStatementStatus.CONFIRMED))).fetch();
    }

    /**
     * 根据状态查询工资表
     * */
    public List<SalaryStatementEntity> querySalaryStatementByStatus(SalaryStatementStatus status) {
        return querySalaryStatement(t -> t.status.eq(status).and(t.deleted.eq(false))).fetch();
    }

    public List<SalaryDetailEntity> querySalaryDetailByStatementId(Long statementId) {
        return querySalaryDetail(t -> t.salaryStatementId.eq(statementId)).fetch();
    }

    public SalaryStatementEntity queryPreviousIncomeDeduction(Long id) {
        return em.find(SalaryStatementEntity.class, id);
    }

    public List<PreviousIncomeDeductionEntity> queryPreviousIncomeDeduction(String taxPeriod, Long supplierCorporationId) {
        return queryPreviousIncomeDeduction(t -> t.taxPeriod.eq(taxPeriod).and(t.supplierCorporationId.eq(supplierCorporationId))).fetch();
    }

    public JPAQuery<SalaryStatementEntity> querySalaryStatement(Function<QSalaryStatementEntity, Predicate> condition) {
        QSalaryStatementEntity t = QSalaryStatementEntity.salaryStatementEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<SalaryDetailEntity> querySalaryDetail(Function<QSalaryDetailEntity, Predicate> condition) {
        QSalaryDetailEntity t = QSalaryDetailEntity.salaryDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    private JPAQuery<PreviousIncomeDeductionEntity> queryPreviousIncomeDeduction(Function<QPreviousIncomeDeductionEntity, Predicate> condition) {
        QPreviousIncomeDeductionEntity t = QPreviousIncomeDeductionEntity.previousIncomeDeductionEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    public void updateSalaryStatement(SalaryStatementEntity entity) {
        em.merge(entity);
    }

    public void saveSalaryStatement(SalaryStatementEntity salaryStatementEntity) {
        em.persist(salaryStatementEntity);
    }

    public void saveSalaryDetail(SalaryDetailEntity salaryDetailEntity) {
        em.persist(salaryDetailEntity);
    }

    public void savePreviousIncomeDeduction(PreviousIncomeDeductionEntity previousIncomeDeductionEntity) {
        em.persist(previousIncomeDeductionEntity);
    }


    public void updateSalaryDetail(SalaryDetailEntity salaryDetailEntity) {
        em.merge(salaryDetailEntity);
    }

    public SalaryDetailEntity querySalaryDetail(Long id) {
        return em.find(SalaryDetailEntity.class, id);
    }

    /**
     * 查询个人在指定主体和税期下的最后一笔已确认薪酬明细
     */
    public SalaryDetailEntity queryLastConfirmedSalaryDetail(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity t = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity s = QSalaryStatementEntity.salaryStatementEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .leftJoin(s).on(t.salaryStatementId.eq(s.id))
                .where(t.idCard.eq(idCard)
                        .and(t.supplierCorporationId.eq(supplierCorporationId))
                        .and(t.taxPeriod.eq(taxPeriod))
                        .and(s.status.eq(SalaryStatementStatus.CONFIRMED))
                )
                .orderBy(t.accumulatedIncome.desc(), t.id.desc())
                .fetchFirst();
    }

    /**
     * 统计个人在指定主体和税期下的薪酬明细数量
     */
    public long countSalaryDetailByIdCardAndCorporationAndPeriod(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity t = QSalaryDetailEntity.salaryDetailEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.idCard.eq(idCard)
                        .and(t.supplierCorporationId.eq(supplierCorporationId))
                        .and(t.taxPeriod.eq(taxPeriod))
                        .and(t.deleted.eq(false)))
                .fetchCount();
    }

    /**
     * 根据作业主体ID和税款所属期查询工资明细，关联工资表状态为CONFIRMED，按身份证分组取累计收入最大的记录
     */
    public List<SalaryDetailEntity> querySalaryDetailForTaxDeclare(Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        // 先查询符合条件的所有明细记录
        List<SalaryDetailEntity> allDetails = new JPAQueryFactory(em)
                .select(detail)
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.supplierCorporationId.eq(supplierCorporationId)
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetch();

        // 按身份证分组，取累计收入最大的记录
        Map<String, SalaryDetailEntity> maxAccumulatedIncomeMap = new HashMap<>();
        for (SalaryDetailEntity detailEntity : allDetails) {
            String idCard = detailEntity.getIdCard();
            SalaryDetailEntity existing = maxAccumulatedIncomeMap.get(idCard);
            if (existing == null ||
                detailEntity.getAccumulatedIncome().compareTo(existing.getAccumulatedIncome()) > 0) {
                maxAccumulatedIncomeMap.put(idCard, detailEntity);
            }
        }

        return new ArrayList<>(maxAccumulatedIncomeMap.values());
    }


    /**
     * 查询当月所有已确认薪酬明细的应发金额累计
     * 用于增值税计算
     *
     * @param supplierCorporationId 作业主体ID
     * @param taxPeriod 税款所属期
     * @return 当月应发金额累计
     */
    public BigDecimal queryMonthlyTotalPayableAmount(Long supplierCorporationId, String taxPeriod, String idCard) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.payableAmount.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.supplierCorporationId.eq(supplierCorporationId)
                        .and(detail.idCard.eq(idCard))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    /**
     * 根据身份证号、作业主体ID和税款所属期查询应发金额总和
     * 用于个税申报时计算本期收入
     */
    public BigDecimal queryPayableAmountSumByIdCard(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.payableAmount.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    /**
     * 根据身份证号、作业主体ID和税款所属期查询实发金额总和
     * 用于个税申报时计算实际发放金额
     */
    public BigDecimal queryNetPaymentSumByIdCard(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.netPayment.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    /**
     * 获取指定人员在指定税期的工资明细聚合数据
     * 用于个税申报模板填充
     */
    public SalaryDetailAggregateData getSalaryDetailAggregateData(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        // 查询该人员在该税期的聚合数据
        Tuple result = new JPAQueryFactory(em)
                .select(
                        detail.taxFreeIncome.sum(),
                        detail.otherDeductions.sum(),
                        detail.taxReliefAmount.sum()
                )
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        if (result != null) {
            return new SalaryDetailAggregateData(
                    result.get(detail.taxFreeIncome.sum()),
                    result.get(detail.otherDeductions.sum()),
                    result.get(detail.taxReliefAmount.sum())
            );
        } else {
            return new SalaryDetailAggregateData(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    /**
     * 工资明细聚合数据类
     */
    public static class SalaryDetailAggregateData {
        private final BigDecimal totalTaxFreeIncome;
        private final BigDecimal totalOtherDeductions;
        private final BigDecimal totalTaxReliefAmount;

        public SalaryDetailAggregateData(BigDecimal totalTaxFreeIncome, BigDecimal totalOtherDeductions, BigDecimal totalTaxReliefAmount) {
            this.totalTaxFreeIncome = totalTaxFreeIncome != null ? totalTaxFreeIncome : BigDecimal.ZERO;
            this.totalOtherDeductions = totalOtherDeductions != null ? totalOtherDeductions : BigDecimal.ZERO;
            this.totalTaxReliefAmount = totalTaxReliefAmount != null ? totalTaxReliefAmount : BigDecimal.ZERO;
        }

        public BigDecimal getTotalTaxFreeIncome() {
            return totalTaxFreeIncome;
        }

        public BigDecimal getTotalOtherDeductions() {
            return totalOtherDeductions;
        }

        public BigDecimal getTotalTaxReliefAmount() {
            return totalTaxReliefAmount;
        }
    }

    public void deleteSalaryStatement(SalaryStatementEntity salaryStatementEntity) {
        em.remove(salaryStatementEntity);
    }

    public void deleteSalaryDetailBySalaryStatementId(Long salaryStatementId) {
        deleteSalaryDetail(t -> t.salaryStatementId.eq(salaryStatementId));
    }

    public long deleteSalaryDetail(Function<QSalaryDetailEntity, Predicate> condition) {
        return new JPAQueryFactory(em)
                .delete(QSalaryDetailEntity.salaryDetailEntity)
                .where(condition.apply(QSalaryDetailEntity.salaryDetailEntity))
                .execute();
    }

    /**
     * 提取指定批次下人员，在整个平台该税期累计薪酬金额超过10万元的人员，按"姓名-身份证号"格式组成列表
     *
     * @param statementId 指定的发薪批次ID
     * @return "姓名-身份证号"格式的列表
     */
    public List<String> queryHighSalaryEmployeesInStatementByTotalAmount(Long statementId) {
        // 先查询指定批次的信息
        final SalaryStatementEntity salaryStatementEntity = this.querySalaryStatement(statementId);
        if (salaryStatementEntity == null) {
            return new ArrayList<>();
        }
        final CorporationConfigEntity corporationConfig = corporationManager.getCorporationConfig(salaryStatementEntity.getSupplierCorporationId());
        final BigDecimal vatStart = corporationConfig.getVatStart().multiply(BigDecimal.valueOf(10000));
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        // 获取指定批次中的所有员工身份证号
        List<String> employeeIdCards = new JPAQueryFactory(em)
                .select(detail.idCard)
                .from(detail)
                .where(detail.salaryStatementId.eq(statementId))
                .fetch();

        if (employeeIdCards.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询这些员工在该税期下所有已确认批次的累计薪酬，筛选超过10万的人员
        List<Tuple> results = new JPAQueryFactory(em)
                .select(detail.name, detail.idCard, detail.payableAmount.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.in(employeeIdCards)
                        .and(detail.taxPeriod.eq(salaryStatementEntity.getTaxPeriod())))
                .groupBy(detail.idCard, detail.name)
                .having(detail.payableAmount.sum().gt(vatStart))
                .fetch();

        // 转换为"姓名-身份证号"格式的列表
        List<String> highSalaryEmployees = new ArrayList<>();
        for (Tuple tuple : results) {
            String name = tuple.get(0, String.class);
            String idCard = tuple.get(1, String.class);
            highSalaryEmployees.add(name + "-" + idCard);
        }

        return highSalaryEmployees;
    }

}
