package com.olading.operate.labor.domain.salary;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

@Getter
@Setter
@Comment("工资表")
@Entity
@Table(name = "t_salary_statement", schema = "olading_labor")
public class SalaryStatementEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("工资表ID")
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Comment("客户名称ID")
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Comment("服务合同合同名称ID")
    @Column(name = "contract_id", nullable = false)
    private Long contractId;

    @NotNull
    @Comment("服务商名称ID")
    @Column(name = "supplier_id", nullable = false)
    private Long supplierId;

    @NotNull
    @Comment("作业主体ID")
    @Column(name = "supplier_corporation_id", nullable = false)
    private Long supplierCorporationId;

    @Size(max = 7)
    @NotNull
    @Comment("税款所属期（格式：2025-06）")
    @Column(name = "tax_period", nullable = false, length = 7)
    private String taxPeriod;

    @Comment("总人数")
    @ColumnDefault("'0'")
    @Column(name = "total_people", columnDefinition = "int UNSIGNED")
    private Long totalPeople;

    @Comment("应发金额总计")
    @ColumnDefault("0.00")
    @Column(name = "total_payable", precision = 16, scale = 2)
    private BigDecimal totalPayable;

    @Comment("个税预缴额总计")
    @ColumnDefault("0.00")
    @Column(name = "total_income_tax", precision = 16, scale = 2)
    private BigDecimal totalIncomeTax;

    @Comment("增值税应纳税额总计")
    @ColumnDefault("0.00")
    @Column(name = "total_vat", precision = 16, scale = 2)
    private BigDecimal totalVat;

    @Comment("附加税应纳税额总计")
    @ColumnDefault("0.00")
    @Column(name = "total_surtax", precision = 16, scale = 2)
    private BigDecimal totalSurtax;

    @Comment("实发金额总计")
    @ColumnDefault("0.00")
    @Column(name = "net_payment_total", precision = 16, scale = 2)
    private BigDecimal netPaymentTotal;

    @Size(max = 7)
    @NotNull
    @Comment("个税申报月（格式：2025-07）")
    @Column(name = "tax_declaration_month", nullable = false, length = 7)
    private String taxDeclarationMonth;

    @Comment("状态（CALCULATING=算税中,UNCONFIRMED=待确认,CONFIRMED=已确认）")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private SalaryStatementStatus status;

    @NotNull
    @Comment("上传时间")
    @Column(name = "upload_time", nullable = false)
    private LocalDateTime uploadTime;

    public SalaryStatementEntity(TenantInfo info){
        setTenant(info);
    }
    public SalaryStatementEntity(){
    }

}