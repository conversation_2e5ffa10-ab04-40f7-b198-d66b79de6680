package com.olading.operate.labor.domain.salary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PreviousIncomeDeductionVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;

    @Schema(description = "作业主体名称")
    private String supplierCorporationName;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "身份证号")
    private String idNumber;

    @Schema(description = "累计收入")
    private BigDecimal accumulatedIncome;

    @Schema(description = "累计费用")
    private BigDecimal accumulatedExpenses;

    @Schema(description = "累计免税收入")
    private BigDecimal accumulatedTaxFreeIncome;

    @Schema(description = "累计依法确定的其他扣除")
    private BigDecimal accumulatedOtherDeductions;

    @Schema(description = "累计已预缴税额")
    private BigDecimal accumulatedPrepaidTax;

    @Schema(description = "累计减免税额")
    private BigDecimal accumulatedTaxReductions;

    @Schema(description = "税款所属期（格式：2025-06）")
    private String taxPeriod;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "服务商ID")
    private Long supplierId;
    
}
