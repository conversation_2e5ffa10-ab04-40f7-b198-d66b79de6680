package com.olading.operate.labor.domain.share.labor;

import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.util.IDCardUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class SupplierLaborManager {

    private final LaborInfoRepository laborInfoRepository;

    public Long createSupplierLabor(SupplierLaborVo param, Long supplierId, TenantInfo tenantInfo) {
        IDCardUtil.checkIdNumber(param.getIdCard());
        List<SupplierLaborEntity> cellphoneLabors = laborInfoRepository.findLaborByCellphone(param.getIdCard(), supplierId);
        if (CollectionUtils.isNotEmpty(cellphoneLabors)) {
            cellphoneLabors = cellphoneLabors.stream().filter(labor -> !labor.getIdCard().equals(param.getIdCard())).toList();
            if (CollectionUtils.isNotEmpty(cellphoneLabors)) {
                throw new ApiException("手机号已被" + cellphoneLabors.get(0).getName() +"占用", ApiException.SYSTEM_ERROR);
            }
        }
        SupplierLaborEntity laborEntity = laborInfoRepository.findLaborByIdCard(param.getIdCard(), supplierId);
        if (laborEntity == null) {
            laborEntity = param.toLaborEntity(supplierId, tenantInfo);
            laborInfoRepository.saveLabor(laborEntity);
        } else {
            if (!laborEntity.getCellphone().equals(param.getCellphone())) {
                throw new ApiException("证件号已存在但与当前手机号不一致", ApiException.SYSTEM_ERROR);
            }
            if (!laborEntity.getName().equals(param.getName())) {
                throw new ApiException("证件号已存在但与当前姓名不一致", ApiException.SYSTEM_ERROR);
            }
        }
        List<LaborInfoEntity> laborInfoEntities = laborInfoRepository.findLaborInfoByContract(supplierId, laborEntity.getId(), param.getContractId());
        if (CollectionUtils.isNotEmpty(laborInfoEntities)) {
            throw new ApiException("服务合同下已存在人员", ApiException.SYSTEM_ERROR);
        }
        LaborInfoEntity laborInfoEntity = param.toLaborInfo(null);
        laborInfoEntity.setLaborId(laborEntity.getId());
        laborInfoEntity.setSupplierId(supplierId);
        laborInfoEntity.setContractId(param.getContractId());
        laborInfoEntity.setSupplierCorporationId(param.getCorporationId());
        laborInfoRepository.saveLaborInfo(laborInfoEntity);
        return laborInfoEntity.getId();
    }
}
