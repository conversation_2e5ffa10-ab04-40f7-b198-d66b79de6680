package com.olading.operate.labor.domain.share.tax;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.TaxDeclareStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.PersonalIncomeTaxDetailVo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.util.ThreadPoolUtil;
import com.olading.operate.labor.util.Utils;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Transactional
@Component
@RequiredArgsConstructor
@Slf4j
public class PersonalIncomeTaxDeclareManager {

    private final EntityManager em;
    private final CorporationManager corporationManager;
    private final PersonalIncomeTaxDetailManager personalIncomeTaxDetailManager;
    private final PlatformTransactionManager transactionManager;
    private final SalaryManager salaryManager;

    /**
     * 新增个税申报记录
     */
    public PersonalIncomeTaxDeclareEntity addPersonalIncomeTaxDeclare(TenantInfo tenantInfo, PersonalIncomeTaxDeclareVo vo) {
        // 1. 检查是否存在相同的作业主体ID + 税款所属期的记录
        if (vo.getSupplierCorporationId() != null && vo.getTaxPaymentPeriod() != null) {
            PersonalIncomeTaxDeclareEntity existingEntity = queryPersonalIncomeTaxDeclareBySupplierCorporationAndTaxPeriod(
                    vo.getSupplierCorporationId(), vo.getTaxPaymentPeriod());

            if (existingEntity != null) {
                // 如果存在且已申报，直接报错
                if (TaxDeclareStatusEnum.DECLARED.name().equals(existingEntity.getTaxStatus())) {
                    throw new BusinessException("存在已申报记录，无法生成");
                }
                // 如果存在但未申报，先删除原有记录及其明细
                deletePersonalIncomeTaxDeclareWithDetails(existingEntity.getId());
                em.flush();
            }
        }

        // 2. 创建申报主记录，默认状态为生成中，申报状态为未申报
        PersonalIncomeTaxDeclareEntity entity = new PersonalIncomeTaxDeclareEntity(tenantInfo);
        copyToEntity(vo, entity);
        entity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATING.name());
        entity.setTaxStatus(TaxDeclareStatusEnum.NOT_DECLARED.name());
        entity = em.merge(entity);
        em.flush();

        // 3. 从工资明细表获取数据并异步处理明细数据
        final Long entityId = entity.getId();
        ThreadPoolUtil.executeCommonThreadPool(() -> {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = transactionManager.getTransaction(def);

            try {
                log.info("开始异步处理个税申报明细数据，申报ID: {}", entityId);

                // 从工资明细表获取数据
                List<PersonalIncomeTaxDetailVo> details = getSalaryDetailDataForTaxDeclare(
                        vo.getSupplierCorporationId(), vo.getTaxPaymentPeriod());

                if (!CollectionUtils.isEmpty(details)) {
                    processDetailsAsyncWithoutTransaction(entityId, details, tenantInfo);
                } else {
                    // 如果没有明细数据，直接设置为已生成状态
                    PersonalIncomeTaxDeclareEntity noDataEntity = findPersonalIncomeTaxDeclareByIdSafely(entityId);
                    if (noDataEntity != null) {
                        noDataEntity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATED.name());
                        em.merge(noDataEntity);
                    } else {
                        log.error("无法找到个税申报记录，ID: {}", entityId);
                    }
                }

                transactionManager.commit(status);
                log.info("个税申报明细数据处理完成，申报ID: {}", entityId);
            } catch (Exception e) {
                transactionManager.rollback(status);
                log.error("异步处理个税申报明细数据失败，申报ID: {}", entityId, e);
            }
        });

        return entity;
    }

    /**
     * 异步处理明细数据（不带事务注解）
     */
    public void processDetailsAsyncWithoutTransaction(Long entityId, List<PersonalIncomeTaxDetailVo> details, TenantInfo tenantInfo) {
        try {
            // 获取申报记录
            PersonalIncomeTaxDeclareEntity entity = findPersonalIncomeTaxDeclareByIdSafely(entityId);
            if (entity == null) {
                log.error("无法找到个税申报记录，ID: {}", entityId);
                return;
            }

            // 处理明细数据
            List<PersonalIncomeTaxDetailEntity> detailEntities = new ArrayList<>();
            for (PersonalIncomeTaxDetailVo detailVo : details) {
                PersonalIncomeTaxDetailEntity detailEntity = convertDetailVoToEntity(detailVo, entity, tenantInfo);
                detailEntities.add(detailEntity);
            }

            // 批量插入明细记录
            personalIncomeTaxDetailManager.batchAddPersonalIncomeTaxDetail(detailEntities);

            // 更新汇总数据
            updateSummaryData(entity);

            entity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATED.name());
            em.merge(entity);

        } catch (Exception e) {
            log.error("异步处理明细数据失败，申报ID: {}", entityId, e);
            throw e;
        }
    }

    /**
     * 安全地根据ID查询个税申报记录（不抛出异常）
     */
    public PersonalIncomeTaxDeclareEntity findPersonalIncomeTaxDeclareByIdSafely(Long id) {
        if (id == null) {
            return null;
        }
        return em.find(PersonalIncomeTaxDeclareEntity.class, id);
    }

    /**
     * 更新个税申报记录
     */
    public PersonalIncomeTaxDeclareEntity updatePersonalIncomeTaxDeclare(PersonalIncomeTaxDeclareVo vo) {
        PersonalIncomeTaxDeclareEntity entity = getPersonalIncomeTaxDeclareById(vo.getId());
        if (entity == null) {
            throw new IllegalStateException("个税申报记录不存在");
        }
        copyToEntityWithNullCheck(vo, entity);
        return em.merge(entity);
    }

    /**
     * 根据ID查询个税申报记录
     */
    public PersonalIncomeTaxDeclareEntity getPersonalIncomeTaxDeclareById(Long id) {
        PersonalIncomeTaxDeclareEntity entity = em.find(PersonalIncomeTaxDeclareEntity.class, id);
        if (entity == null) {
            throw new IllegalStateException("个税申报记录不存在");
        }
        return entity;
    }

    /**
     * 查询个税申报记录详情
     */
    public PersonalIncomeTaxDeclareVo queryPersonalIncomeTaxDeclare(Long id) {
        PersonalIncomeTaxDeclareEntity entity = getPersonalIncomeTaxDeclareById(id);
        PersonalIncomeTaxDeclareVo vo = new PersonalIncomeTaxDeclareVo();
        copyFromEntity(entity, vo);

        // 设置作业主体名称
        if (entity.getSupplierCorporationId() != null) {
            try {
                vo.setSupplierCorporationName(corporationManager.requireCorporation(entity.getSupplierCorporationId()).getName());
            } catch (Exception e) {
                // 如果作业主体不存在，设置为空
                vo.setSupplierCorporationName(null);
            }
        }

        // 统计本期应预扣预缴税额
        BigDecimal currentWithholdingTax = personalIncomeTaxDetailManager.sumCurrentWithholdingTaxByTaxDeclareId(id);
        vo.setCurrentWithholdingTax(currentWithholdingTax);

        // 统计总实发金额
        BigDecimal totalActualAmount = personalIncomeTaxDetailManager.sumActualAmountByTaxDeclareId(id);
        vo.setTotalActualAmount(totalActualAmount);

        // 查询并设置明细数据
        List<PersonalIncomeTaxDetailEntity> detailEntities = personalIncomeTaxDetailManager.queryDetailsByTaxDeclareId(id);
        if (!CollectionUtils.isEmpty(detailEntities)) {
            List<PersonalIncomeTaxDetailVo> detailVos = detailEntities.stream()
                    .map(this::convertDetailEntityToVo)
                    .collect(Collectors.toList());
            vo.setDetails(detailVos);
        }

        return vo;
    }

    /**
     * 根据作业主体ID查询个税申报记录列表
     */
    public List<PersonalIncomeTaxDeclareEntity> queryPersonalIncomeTaxDeclareBySupplierCorporation(Long supplierCorporationId) {
        QPersonalIncomeTaxDeclareEntity t = QPersonalIncomeTaxDeclareEntity.personalIncomeTaxDeclareEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.supplierCorporationId.eq(supplierCorporationId))
                .fetch();
    }

    /**
     * 根据作业主体ID和税款所属期查询个税申报记录
     */
    public PersonalIncomeTaxDeclareEntity queryPersonalIncomeTaxDeclareBySupplierCorporationAndTaxPeriod(Long supplierCorporationId, String taxPaymentPeriod) {
        QPersonalIncomeTaxDeclareEntity t = QPersonalIncomeTaxDeclareEntity.personalIncomeTaxDeclareEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.supplierCorporationId.eq(supplierCorporationId)
                        .and(t.taxPaymentPeriod.eq(taxPaymentPeriod)))
                .fetchOne();
    }

    /**
     * 删除个税申报记录（直接删除）
     */
    public void deletePersonalIncomeTaxDeclare(Long id) {
        PersonalIncomeTaxDeclareEntity entity = getPersonalIncomeTaxDeclareById(id);
        em.remove(entity);
    }

    /**
     * 删除个税申报记录及其明细（先删明细，再删主记录）
     */
    public void deletePersonalIncomeTaxDeclareWithDetails(Long id) {
        // 先删除明细记录
        personalIncomeTaxDetailManager.deleteDetailsByTaxDeclareId(id);

        // 再删除主记录
        PersonalIncomeTaxDeclareEntity entity = getPersonalIncomeTaxDeclareById(id);
        em.remove(entity);
    }

    /**
     * 通用查询方法
     */
    public JPAQuery<PersonalIncomeTaxDeclareEntity> queryPersonalIncomeTaxDeclare(Function<QPersonalIncomeTaxDeclareEntity, Predicate> condition) {
        QPersonalIncomeTaxDeclareEntity t = QPersonalIncomeTaxDeclareEntity.personalIncomeTaxDeclareEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    /**
     * 复制VO到Entity
     */
    private void copyToEntity(PersonalIncomeTaxDeclareVo vo, PersonalIncomeTaxDeclareEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getTaxPaymentPeriod() != null) {
            entity.setTaxPaymentPeriod(vo.getTaxPaymentPeriod());
            entity.setIncomeTaxMonth(Utils.calculateDeclareMonth(vo.getTaxPaymentPeriod()));
        }
        if (vo.getTaxpayersCount() != null) {
            entity.setTaxpayersCount(vo.getTaxpayersCount());
        }
        if (vo.getCurrentIncome() != null) {
            entity.setCurrentIncome(vo.getCurrentIncome());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getStatus() != null) {
            entity.setStatus(vo.getStatus());
        }
        if (vo.getTaxStatus() != null) {
            entity.setTaxStatus(vo.getTaxStatus());
        }
    }

    /**
     * 复制VO到Entity（空值检查）
     */
    private void copyToEntityWithNullCheck(PersonalIncomeTaxDeclareVo vo, PersonalIncomeTaxDeclareEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getTaxPaymentPeriod() != null) {
            entity.setTaxPaymentPeriod(vo.getTaxPaymentPeriod());
        }
        if (vo.getIncomeTaxMonth() != null) {
            entity.setIncomeTaxMonth(vo.getIncomeTaxMonth());
        }
        if (vo.getTaxpayersCount() != null) {
            entity.setTaxpayersCount(vo.getTaxpayersCount());
        }
        if (vo.getCurrentIncome() != null) {
            entity.setCurrentIncome(vo.getCurrentIncome());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getStatus() != null) {
            entity.setStatus(vo.getStatus());
        }
        if (vo.getTaxStatus() != null) {
            entity.setTaxStatus(vo.getTaxStatus());
        }
    }

    /**
     * 更新申报状态为已申报
     */
    public void updateTaxStatusToDeclared(Long id) {
        PersonalIncomeTaxDeclareEntity entity = getPersonalIncomeTaxDeclareById(id);
        entity.setTaxStatus(TaxDeclareStatusEnum.DECLARED.name());
        em.merge(entity);
    }

    /**
     * 复制Entity到VO
     */
    private void copyFromEntity(PersonalIncomeTaxDeclareEntity entity, PersonalIncomeTaxDeclareVo vo) {
        vo.setId(entity.getId());
        vo.setSupplierCorporationId(entity.getSupplierCorporationId());
        vo.setTaxPaymentPeriod(entity.getTaxPaymentPeriod());
        vo.setIncomeTaxMonth(entity.getIncomeTaxMonth());
        vo.setTaxpayersCount(entity.getTaxpayersCount());
        vo.setCurrentIncome(entity.getCurrentIncome());
        vo.setCreateTime(entity.getCreateTime());
        vo.setModifyTime(entity.getModifyTime());
        vo.setSupplierId(entity.getSupplierId());
        vo.setStatus(entity.getStatus());
        vo.setTaxStatus(entity.getTaxStatus());
    }

    /**
     * 将明细VO转换为Entity
     */
    private PersonalIncomeTaxDetailEntity convertDetailVoToEntity(PersonalIncomeTaxDetailVo detailVo,
                                                                  PersonalIncomeTaxDeclareEntity declareEntity,
                                                                  TenantInfo tenantInfo) {
        PersonalIncomeTaxDetailEntity detailEntity = new PersonalIncomeTaxDetailEntity(tenantInfo);

        // 设置关联关系
        detailEntity.setTaxDeclareId(declareEntity.getId());
        detailEntity.setSupplierId(declareEntity.getSupplierId());
        detailEntity.setSupplierCorporationId(declareEntity.getSupplierCorporationId());

        // 设置基本信息
        detailEntity.setName(detailVo.getName());
        detailEntity.setIdCard(detailVo.getIdCard());
        detailEntity.setTaxPeriod(declareEntity.getTaxPaymentPeriod());
        detailEntity.setDeclareMonth(declareEntity.getIncomeTaxMonth());
        detailEntity.setCellphone(detailVo.getCellphone());

        // 设置金额信息（如果为null则设置为0）
        // 修改：currentIncome 设置为该身份证在指定税期下的 payable_amount 总和
        BigDecimal currentIncomeFromSalary = salaryManager.queryPayableAmountSumByIdCard(
                detailVo.getIdCard(),
                declareEntity.getSupplierCorporationId(),
                declareEntity.getTaxPaymentPeriod());
        detailEntity.setCurrentIncome(currentIncomeFromSalary);

        detailEntity.setAccumulatedIncome(detailVo.getAccumulatedIncome() != null ? detailVo.getAccumulatedIncome() : BigDecimal.ZERO);
        detailEntity.setAccumulatedExpenses(detailVo.getAccumulatedExpenses() != null ? detailVo.getAccumulatedExpenses() : BigDecimal.ZERO);
        detailEntity.setAccumulatedTaxFreeIncome(detailVo.getAccumulatedTaxFreeIncome() != null ? detailVo.getAccumulatedTaxFreeIncome() : BigDecimal.ZERO);
        detailEntity.setAccumulatedOtherDeductions(detailVo.getAccumulatedOtherDeductions() != null ? detailVo.getAccumulatedOtherDeductions() : BigDecimal.ZERO);

        // 累计已预缴税额 = 累计已预缴税额 + 本期应预扣预缴税额
        BigDecimal accumulatedPrepaidTax = (detailVo.getAccumulatedPrepaidTax() != null ? detailVo.getAccumulatedPrepaidTax() : BigDecimal.ZERO)
                .add(detailVo.getCurrentWithholdingTax() != null ? detailVo.getCurrentWithholdingTax() : BigDecimal.ZERO);
        detailEntity.setAccumulatedPrepaidTax(accumulatedPrepaidTax);

        //actualAmount 当前所属期actualAmount的总和
        //actualAmount 设置为该身份证在指定税期下的 net_payment 总和
        BigDecimal actualAmountFromSalary = salaryManager.queryNetPaymentSumByIdCard(
                detailVo.getIdCard(),
                declareEntity.getSupplierCorporationId(),
                declareEntity.getTaxPaymentPeriod());

        detailEntity.setCurrentWithholdingTax(detailVo.getCurrentWithholdingTax() != null ? detailVo.getCurrentWithholdingTax() : BigDecimal.ZERO);
        detailEntity.setActualAmount(actualAmountFromSalary);
        detailEntity.setAccumulatedTaxDeductionExpenses(detailVo.getAccumulatedTaxDeductionExpenses() != null ? detailVo.getAccumulatedTaxDeductionExpenses() : BigDecimal.ZERO);
        detailEntity.setAccumulatedTaxReductions(detailVo.getAccumulatedTaxReductions() != null ? detailVo.getAccumulatedTaxReductions() : BigDecimal.ZERO);
        detailEntity.setAccumulatedTaxableAmount(detailVo.getAccumulatedTaxableAmount() != null ? detailVo.getAccumulatedTaxableAmount() : BigDecimal.ZERO);

        return detailEntity;
    }

    /**
     * 更新汇总数据
     */
    private void updateSummaryData(PersonalIncomeTaxDeclareEntity entity) {
        // 统计纳税人数（明细条数）
        Long taxpayersCount = personalIncomeTaxDetailManager.countTaxpayersByTaxDeclareId(entity.getId());
        entity.setTaxpayersCount(String.valueOf(taxpayersCount));

        // 统计本期收入总和
        BigDecimal totalCurrentIncome = personalIncomeTaxDetailManager.sumCurrentIncomeByTaxDeclareId(entity.getId());
        entity.setCurrentIncome(totalCurrentIncome.toString());

        em.merge(entity);
    }

    /**
     * 将明细Entity转换为VO
     */
    private PersonalIncomeTaxDetailVo convertDetailEntityToVo(PersonalIncomeTaxDetailEntity entity) {
        PersonalIncomeTaxDetailVo vo = new PersonalIncomeTaxDetailVo();

        vo.setId(entity.getId());
        vo.setSupplierId(entity.getSupplierId());
        vo.setSupplierCorporationId(entity.getSupplierCorporationId());
        vo.setTaxDeclareId(entity.getTaxDeclareId());
        vo.setName(entity.getName());
        vo.setIdCard(entity.getIdCard());
        vo.setCurrentIncome(entity.getCurrentIncome());
        vo.setAccumulatedIncome(entity.getAccumulatedIncome());
        vo.setAccumulatedExpenses(entity.getAccumulatedExpenses());
        vo.setAccumulatedTaxFreeIncome(entity.getAccumulatedTaxFreeIncome());
        vo.setAccumulatedOtherDeductions(entity.getAccumulatedOtherDeductions());
        vo.setAccumulatedPrepaidTax(entity.getAccumulatedPrepaidTax());
        vo.setCurrentWithholdingTax(entity.getCurrentWithholdingTax());
        vo.setTaxPeriod(entity.getTaxPeriod());
        vo.setDeclareMonth(entity.getDeclareMonth());
        vo.setCellphone(entity.getCellphone());
        vo.setActualAmount(entity.getActualAmount());
        vo.setAccumulatedTaxDeductionExpenses(entity.getAccumulatedTaxDeductionExpenses());
        vo.setAccumulatedTaxReductions(entity.getAccumulatedTaxReductions());
        vo.setAccumulatedTaxableAmount(entity.getAccumulatedTaxableAmount());

        return vo;
    }

    /**
     * 从工资明细表获取个税申报数据
     */
    private List<PersonalIncomeTaxDetailVo> getSalaryDetailDataForTaxDeclare(Long supplierCorporationId, String taxPeriod) {
        // 从工资明细表获取数据
        List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailForTaxDeclare(supplierCorporationId, taxPeriod);

        List<PersonalIncomeTaxDetailVo> detailVos = new ArrayList<>();
        for (SalaryDetailEntity salaryDetail : salaryDetails) {
            PersonalIncomeTaxDetailVo detailVo = convertSalaryDetailToTaxDetailVo(salaryDetail, taxPeriod);
            detailVos.add(detailVo);
        }

        return detailVos;
    }

    /**
     * 将工资明细转换为个税申报明细VO
     */
    private PersonalIncomeTaxDetailVo convertSalaryDetailToTaxDetailVo(SalaryDetailEntity salaryDetail, String taxPeriod) {
        PersonalIncomeTaxDetailVo detailVo = new PersonalIncomeTaxDetailVo();

        // 基本信息映射
        detailVo.setName(salaryDetail.getName());
        detailVo.setIdCard(salaryDetail.getIdCard());
        detailVo.setCellphone(salaryDetail.getPhoneNumber());

        // 金额字段映射
        detailVo.setCurrentIncome(salaryDetail.getPayableAmount());
        detailVo.setActualAmount(salaryDetail.getNetPayment());
        detailVo.setAccumulatedIncome(salaryDetail.getAccumulatedIncome());
        detailVo.setAccumulatedTaxFreeIncome(salaryDetail.getAccumulatedTaxFreeIncome());

        detailVo.setAccumulatedExpenses(salaryDetail.getAccumulatedExpenses());
        detailVo.setAccumulatedTaxDeductionExpenses(salaryDetail.getAccumulatedDeductionExpenses());
        detailVo.setAccumulatedOtherDeductions(salaryDetail.getAccumulatedOtherDeductions());
        detailVo.setAccumulatedPrepaidTax(salaryDetail.getAccumulatedPrepaidTax());
        detailVo.setAccumulatedTaxReductions(salaryDetail.getAccumulatedTaxRelief());
        detailVo.setAccumulatedTaxableAmount(salaryDetail.getAccumulatedTaxAmount());
        detailVo.setCurrentWithholdingTax(salaryDetail.getCurrentTaxAmount()); // 修改为映射到 current_tax_amount

        return detailVo;
    }

}
