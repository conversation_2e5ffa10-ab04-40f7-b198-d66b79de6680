package com.olading.operate.labor.domain.share.tax;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.TaxDeclareStatusEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.SurtaxCodeEnum;
import com.olading.operate.labor.domain.corporation.SurtaxData;
import com.olading.operate.labor.domain.salary.QSalaryDetailEntity;
import com.olading.operate.labor.domain.salary.QSalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDeclareVo;
import com.olading.operate.labor.domain.share.tax.vo.ValueAddedTaxDetailVo;
import com.olading.operate.labor.util.ThreadPoolUtil;
import com.olading.operate.labor.util.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Transactional
@Component
@RequiredArgsConstructor
@Slf4j
public class ValueAddedTaxDeclareManager {

    private final EntityManager em;
    private final CorporationManager corporationManager;
    private final ValueAddedTaxDetailManager valueAddedTaxDetailManager;
    private final PlatformTransactionManager transactionManager;
    private final SalaryManager salaryManager;

    /**
     * 新增增值税申报记录
     */
    public ValueAddedTaxDeclareEntity addValueAddedTaxDeclare(TenantInfo tenantInfo, ValueAddedTaxDeclareVo vo) {
        // 检查是否存在相同的作业主体ID + 税款所属期的记录
        if (vo.getSupplierCorporationId() != null && vo.getTaxPaymentPeriod() != null) {
            ValueAddedTaxDeclareEntity existingEntity = queryValueAddedTaxDeclareBySupplierCorporationAndTaxPeriod(
                    vo.getSupplierCorporationId(), vo.getTaxPaymentPeriod());

            if (existingEntity != null) {
                // 如果存在且已申报，直接报错
                if (TaxDeclareStatusEnum.DECLARED.name().equals(existingEntity.getTaxStatus())) {
                    throw new BusinessException("存在已申报记录，无法生成");
                }
                // 如果存在但未申报，先删除原有记录及其明细
                deleteValueAddedTaxDeclareWithDetails(existingEntity.getId());
                em.flush();
            }
        }

        // 创建申报主记录，默认状态为生成中，申报状态为未申报
        ValueAddedTaxDeclareEntity entity = new ValueAddedTaxDeclareEntity(tenantInfo);
        copyToEntity(vo, entity);
        entity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATING.name());
        entity.setTaxStatus(TaxDeclareStatusEnum.NOT_DECLARED.name());
        entity = em.merge(entity);
        em.flush();

        // 异步生成和处理明细数据
        final Long entityId = entity.getId();
        final Long supplierCorporationId = vo.getSupplierCorporationId();
        final String taxPaymentPeriod = vo.getTaxPaymentPeriod();

        ThreadPoolUtil.executeCommonThreadPool(() -> {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = transactionManager.getTransaction(def);

            try {
                log.info("开始异步生成和处理增值税申报明细数据，申报ID: {}", entityId);

                // 生成增值税申报明细数据
                List<ValueAddedTaxDetailVo> detailVos = generateValueAddedTaxDetails(
                        supplierCorporationId, taxPaymentPeriod);

                if (!CollectionUtils.isEmpty(detailVos)) {
                    // 处理明细数据
                    processDetailsAsyncWithoutTransaction(entityId, detailVos, tenantInfo);
                    log.info("增值税申报明细数据处理完成，申报ID: {}，明细数量: {}", entityId, detailVos.size());
                } else {
                    log.info("未找到工资明细数据，申报ID: {}", entityId);
                    // 直接设置为已生成状态
                    ValueAddedTaxDeclareEntity entityToUpdate = findValueAddedTaxDeclareByIdSafely(entityId);
                    if (entityToUpdate != null) {
                        entityToUpdate.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATED.name());
                        em.merge(entityToUpdate);
                    } else {
                        log.error("无法找到增值税申报记录，ID: {}", entityId);
                    }
                }

                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                log.error("异步生成和处理增值税申报明细数据失败，申报ID: {}", entityId, e);
                // 保持在GENERATING状态，不做额外处理
            }
        });

        return entity;
    }

    /**
     * 异步处理明细数据（不带事务注解，在编程式事务中调用）
     */
    public void processDetailsAsyncWithoutTransaction(Long entityId, List<ValueAddedTaxDetailVo> details, TenantInfo tenantInfo) {
        try {
            // 获取申报记录
            ValueAddedTaxDeclareEntity entity = findValueAddedTaxDeclareByIdSafely(entityId);
            if (entity == null) {
                log.error("无法找到增值税申报记录，ID: {}", entityId);
                return;
            }

            // 处理明细数据
            List<ValueAddedTaxDetailEntity> detailEntities = new ArrayList<>();
            for (ValueAddedTaxDetailVo detailVo : details) {
                ValueAddedTaxDetailEntity detailEntity = convertDetailVoToEntity(detailVo, entity, tenantInfo);
                detailEntities.add(detailEntity);
            }

            // 批量插入明细记录
            valueAddedTaxDetailManager.batchAddValueAddedTaxDetail(detailEntities);

            // 更新汇总数据
            updateSummaryData(entity);

            // 更新状态为已生成
            entity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATED.name());
            em.merge(entity);

        } catch (Exception e) {
            log.error("异步处理明细数据失败，申报ID: {}", entityId, e);
            throw e;
        }
    }

    /**
     * 安全地根据ID查询增值税申报记录（不抛出异常）
     */
    public ValueAddedTaxDeclareEntity findValueAddedTaxDeclareByIdSafely(Long id) {
        if (id == null) {
            return null;
        }
        return em.find(ValueAddedTaxDeclareEntity.class, id);
    }

    /**
     * 更新增值税申报记录
     */
    public ValueAddedTaxDeclareEntity updateValueAddedTaxDeclare(ValueAddedTaxDeclareVo vo) {
        ValueAddedTaxDeclareEntity entity = getValueAddedTaxDeclareById(vo.getId());
        if (entity == null) {
            throw new IllegalStateException("增值税申报记录不存在");
        }
        copyToEntityWithNullCheck(vo, entity);
        return em.merge(entity);
    }

    /**
     * 根据ID查询增值税申报记录
     */
    public ValueAddedTaxDeclareEntity getValueAddedTaxDeclareById(Long id) {
        ValueAddedTaxDeclareEntity entity = em.find(ValueAddedTaxDeclareEntity.class, id);
        if (entity == null) {
            throw new IllegalStateException("增值税申报记录不存在");
        }
        return entity;
    }

    /**
     * 查询增值税申报记录详情
     */
    public ValueAddedTaxDeclareVo queryValueAddedTaxDeclare(Long id) {
        ValueAddedTaxDeclareEntity entity = getValueAddedTaxDeclareById(id);
        ValueAddedTaxDeclareVo vo = new ValueAddedTaxDeclareVo();
        copyFromEntity(entity, vo);

        // 设置作业主体名称
        if (entity.getSupplierCorporationId() != null) {
            try {
                vo.setSupplierCorporationName(corporationManager.requireCorporation(entity.getSupplierCorporationId()).getName());
            } catch (Exception e) {
                // 如果作业主体不存在，设置为空
                vo.setSupplierCorporationName(null);
            }
        }

        // 统计增值税总额
        BigDecimal totalVatAmount = valueAddedTaxDetailManager.sumVatAmountByValueAddedTaxId(id);
        vo.setTotalVatAmount(totalVatAmount);

        // 统计附加税总额
        BigDecimal totalSurtaxAmount = valueAddedTaxDetailManager.sumSurtaxAmountByValueAddedTaxId(id);
        vo.setTotalSurtaxAmount(totalSurtaxAmount);

        // 查询并设置明细数据
        List<ValueAddedTaxDetailEntity> detailEntities = valueAddedTaxDetailManager.queryDetailsByValueAddedTaxId(id);
        if (!CollectionUtils.isEmpty(detailEntities)) {
            List<ValueAddedTaxDetailVo> detailVos = detailEntities.stream()
                    .map(this::convertDetailEntityToVo)
                    .collect(Collectors.toList());
            vo.setDetails(detailVos);
        }

        return vo;
    }

    /**
     * 根据作业主体ID查询增值税申报记录列表
     */
    public List<ValueAddedTaxDeclareEntity> queryValueAddedTaxDeclareBySupplierCorporation(Long supplierCorporationId) {
        QValueAddedTaxDeclareEntity t = QValueAddedTaxDeclareEntity.valueAddedTaxDeclareEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.supplierCorporationId.eq(supplierCorporationId))
                .fetch();
    }

    /**
     * 删除增值税申报记录（直接删除）
     */
    public void deleteValueAddedTaxDeclare(Long id) {
        ValueAddedTaxDeclareEntity entity = getValueAddedTaxDeclareById(id);
        em.remove(entity);
    }

    /**
     * 删除增值税申报记录及其明细（先删明细，再删主记录）
     */
    public void deleteValueAddedTaxDeclareWithDetails(Long id) {
        // 先删除明细记录
        valueAddedTaxDetailManager.deleteDetailsByValueAddedTaxId(id);

        // 再删除主记录
        ValueAddedTaxDeclareEntity entity = getValueAddedTaxDeclareById(id);
        em.remove(entity);
    }

    /**
     * 根据作业主体ID和税款所属期查询增值税申报记录
     */
    public ValueAddedTaxDeclareEntity queryValueAddedTaxDeclareBySupplierCorporationAndTaxPeriod(Long supplierCorporationId, String taxPaymentPeriod) {
        QValueAddedTaxDeclareEntity t = QValueAddedTaxDeclareEntity.valueAddedTaxDeclareEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(t.supplierCorporationId.eq(supplierCorporationId)
                        .and(t.taxPaymentPeriod.eq(taxPaymentPeriod)))
                .fetchFirst();
    }

    /**
     * 通用查询方法
     */
    public JPAQuery<ValueAddedTaxDeclareEntity> queryValueAddedTaxDeclare(Function<QValueAddedTaxDeclareEntity, Predicate> condition) {
        QValueAddedTaxDeclareEntity t = QValueAddedTaxDeclareEntity.valueAddedTaxDeclareEntity;
        return new JPAQueryFactory(em)
                .select(t)
                .from(t)
                .where(condition.apply(t));
    }

    /**
     * 复制VO到Entity
     */
    private void copyToEntity(ValueAddedTaxDeclareVo vo, ValueAddedTaxDeclareEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getTaxPaymentPeriod() != null) {
            entity.setTaxPaymentPeriod(vo.getTaxPaymentPeriod());
            // 自动计算申报月份：税款所属期+1个月
            entity.setIncomeTaxMonth(Utils.calculateDeclareMonth(vo.getTaxPaymentPeriod()));
        }
        if (vo.getTaxpayersCount() != null) {
            entity.setTaxpayersCount(vo.getTaxpayersCount());
        }
        if (vo.getCurrentIncome() != null) {
            entity.setCurrentIncome(vo.getCurrentIncome());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getStatus() != null) {
            entity.setStatus(vo.getStatus());
        }
        if (vo.getTaxStatus() != null) {
            entity.setTaxStatus(vo.getTaxStatus());
        }
    }

    /**
     * 复制VO到Entity（空值检查）
     */
    private void copyToEntityWithNullCheck(ValueAddedTaxDeclareVo vo, ValueAddedTaxDeclareEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getTaxPaymentPeriod() != null) {
            entity.setTaxPaymentPeriod(vo.getTaxPaymentPeriod());
        }
        if (vo.getIncomeTaxMonth() != null) {
            entity.setIncomeTaxMonth(vo.getIncomeTaxMonth());
        }
        if (vo.getTaxpayersCount() != null) {
            entity.setTaxpayersCount(vo.getTaxpayersCount());
        }
        if (vo.getCurrentIncome() != null) {
            entity.setCurrentIncome(vo.getCurrentIncome());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getStatus() != null) {
            entity.setStatus(vo.getStatus());
        }
        if (vo.getTaxStatus() != null) {
            entity.setTaxStatus(vo.getTaxStatus());
        }
    }

    /**
     * 更新申报状态为已申报
     */
    public void updateTaxStatusToDeclared(Long id) {
        ValueAddedTaxDeclareEntity entity = getValueAddedTaxDeclareById(id);
        entity.setTaxStatus(TaxDeclareStatusEnum.DECLARED.name());
        em.merge(entity);
    }

    /**
     * 复制Entity到VO
     */
    private void copyFromEntity(ValueAddedTaxDeclareEntity entity, ValueAddedTaxDeclareVo vo) {
        vo.setId(entity.getId());
        vo.setSupplierCorporationId(entity.getSupplierCorporationId());
        vo.setTaxPaymentPeriod(entity.getTaxPaymentPeriod());
        vo.setIncomeTaxMonth(entity.getIncomeTaxMonth());
        vo.setTaxpayersCount(entity.getTaxpayersCount());
        vo.setCurrentIncome(entity.getCurrentIncome());
        vo.setCreateTime(entity.getCreateTime());
        vo.setModifyTime(entity.getModifyTime());
        vo.setSupplierId(entity.getSupplierId());
        vo.setStatus(entity.getStatus());
        vo.setTaxStatus(entity.getTaxStatus());
    }

    /**
     * 将明细VO转换为Entity
     */
    private ValueAddedTaxDetailEntity convertDetailVoToEntity(ValueAddedTaxDetailVo detailVo,
                                                              ValueAddedTaxDeclareEntity declareEntity,
                                                              TenantInfo tenantInfo) {
        ValueAddedTaxDetailEntity detailEntity = new ValueAddedTaxDetailEntity(tenantInfo);

        // 设置关联关系
        detailEntity.setValueAddedTaxId(declareEntity.getId());
        detailEntity.setSupplierId(declareEntity.getSupplierId());
        detailEntity.setSupplierCorporationId(declareEntity.getSupplierCorporationId());

        // 设置基本信息
        detailEntity.setName(detailVo.getName());
        detailEntity.setCertificateType(detailVo.getCertificateType());
        detailEntity.setIdCard(detailVo.getIdCard());
        detailEntity.setCountryRegion(detailVo.getCountryRegion());
        detailEntity.setAddress(detailVo.getAddress());
        detailEntity.setUserName(detailVo.getUserName());
        detailEntity.setUserUniqueCode(detailVo.getUserUniqueCode());
        detailEntity.setTaxPeriod(detailVo.getTaxPeriod());
        detailEntity.setDeclareMonth(detailVo.getDeclareMonth());

        // 设置增值税相关字段
        detailEntity.setTaxBasis(detailVo.getTaxBasis() != null ? detailVo.getTaxBasis() : BigDecimal.ZERO);
        detailEntity.setTaxItem(detailVo.getTaxItem());
        detailEntity.setTaxRate(detailVo.getTaxRate());
        detailEntity.setVatAmount(detailVo.getVatAmount() != null ? detailVo.getVatAmount() : BigDecimal.ZERO);
        detailEntity.setVatExemptionCode(detailVo.getVatExemptionCode());
        detailEntity.setVatExemptionAmount(detailVo.getVatExemptionAmount() != null ? detailVo.getVatExemptionAmount() : BigDecimal.ZERO);
        detailEntity.setVatPayable(detailVo.getVatPayable() != null ? detailVo.getVatPayable() : BigDecimal.ZERO);

        // 设置城建税相关字段
        detailEntity.setUrbanTaxRate(detailVo.getUrbanTaxRate());
        detailEntity.setUrbanTaxAmount(detailVo.getUrbanTaxAmount() != null ? detailVo.getUrbanTaxAmount() : BigDecimal.ZERO);
        detailEntity.setUrbanExemptionCode(detailVo.getUrbanExemptionCode());
        detailEntity.setUrbanExemptionAmount(detailVo.getUrbanExemptionAmount() != null ? detailVo.getUrbanExemptionAmount() : BigDecimal.ZERO);
        detailEntity.setUrbanTaxPayable(detailVo.getUrbanTaxPayable() != null ? detailVo.getUrbanTaxPayable() : BigDecimal.ZERO);

        // 设置教育附加相关字段
        detailEntity.setEduTaxRate(detailVo.getEduTaxRate());
        detailEntity.setEduTaxAmount(detailVo.getEduTaxAmount() != null ? detailVo.getEduTaxAmount() : BigDecimal.ZERO);
        detailEntity.setEduExemptionCode(detailVo.getEduExemptionCode());
        detailEntity.setEduExemptionAmount(detailVo.getEduExemptionAmount() != null ? detailVo.getEduExemptionAmount() : BigDecimal.ZERO);
        detailEntity.setEduTaxPayable(detailVo.getEduTaxPayable() != null ? detailVo.getEduTaxPayable() : BigDecimal.ZERO);

        // 设置地方教育附加相关字段
        detailEntity.setLocalEduTaxRate(detailVo.getLocalEduTaxRate());
        detailEntity.setLocalEduTaxAmount(detailVo.getLocalEduTaxAmount() != null ? detailVo.getLocalEduTaxAmount() : BigDecimal.ZERO);
        detailEntity.setLocalEduExemptionCode(detailVo.getLocalEduExemptionCode());
        detailEntity.setLocalEduExemptionAmount(detailVo.getLocalEduExemptionAmount() != null ? detailVo.getLocalEduExemptionAmount() : BigDecimal.ZERO);
        detailEntity.setLocalEduTaxPayable(detailVo.getLocalEduTaxPayable() != null ? detailVo.getLocalEduTaxPayable() : BigDecimal.ZERO);

        return detailEntity;
    }

    /**
     * 将明细Entity转换为VO
     */
    private ValueAddedTaxDetailVo convertDetailEntityToVo(ValueAddedTaxDetailEntity detailEntity) {
        ValueAddedTaxDetailVo detailVo = new ValueAddedTaxDetailVo();

        // 设置基本信息
        detailVo.setId(detailEntity.getId());
        detailVo.setSupplierId(detailEntity.getSupplierId());
        detailVo.setSupplierCorporationId(detailEntity.getSupplierCorporationId());
        detailVo.setValueAddedTaxId(detailEntity.getValueAddedTaxId());
        detailVo.setName(detailEntity.getName());
        detailVo.setCertificateType(detailEntity.getCertificateType());
        detailVo.setIdCard(detailEntity.getIdCard());
        detailVo.setCountryRegion(detailEntity.getCountryRegion());
        detailVo.setAddress(detailEntity.getAddress());
        detailVo.setUserName(detailEntity.getUserName());
        detailVo.setUserUniqueCode(detailEntity.getUserUniqueCode());
        detailVo.setTaxPeriod(detailEntity.getTaxPeriod());
        detailVo.setDeclareMonth(detailEntity.getDeclareMonth());

        // 设置增值税相关字段
        detailVo.setTaxBasis(detailEntity.getTaxBasis());
        detailVo.setTaxItem(detailEntity.getTaxItem());
        detailVo.setTaxRate(detailEntity.getTaxRate());
        detailVo.setVatAmount(detailEntity.getVatAmount());
        detailVo.setVatExemptionCode(detailEntity.getVatExemptionCode());
        detailVo.setVatExemptionAmount(detailEntity.getVatExemptionAmount());
        detailVo.setVatPayable(detailEntity.getVatPayable());

        // 设置城建税相关字段
        detailVo.setUrbanTaxRate(detailEntity.getUrbanTaxRate());
        detailVo.setUrbanTaxAmount(detailEntity.getUrbanTaxAmount());
        detailVo.setUrbanExemptionCode(detailEntity.getUrbanExemptionCode());
        detailVo.setUrbanExemptionAmount(detailEntity.getUrbanExemptionAmount());
        detailVo.setUrbanTaxPayable(detailEntity.getUrbanTaxPayable());

        // 设置教育附加相关字段
        detailVo.setEduTaxRate(detailEntity.getEduTaxRate());
        detailVo.setEduTaxAmount(detailEntity.getEduTaxAmount());
        detailVo.setEduExemptionCode(detailEntity.getEduExemptionCode());
        detailVo.setEduExemptionAmount(detailEntity.getEduExemptionAmount());
        detailVo.setEduTaxPayable(detailEntity.getEduTaxPayable());

        // 设置地方教育附加相关字段
        detailVo.setLocalEduTaxRate(detailEntity.getLocalEduTaxRate());
        detailVo.setLocalEduTaxAmount(detailEntity.getLocalEduTaxAmount());
        detailVo.setLocalEduExemptionCode(detailEntity.getLocalEduExemptionCode());
        detailVo.setLocalEduExemptionAmount(detailEntity.getLocalEduExemptionAmount());
        detailVo.setLocalEduTaxPayable(detailEntity.getLocalEduTaxPayable());

        return detailVo;
    }

    /**
     * 更新汇总数据
     */
    private void updateSummaryData(ValueAddedTaxDeclareEntity entity) {
        // 统计纳税人数（明细条数）
        Long taxpayersCount = valueAddedTaxDetailManager.countTaxpayersByValueAddedTaxId(entity.getId());
        entity.setTaxpayersCount(String.valueOf(taxpayersCount));

       /* // 统计本期收入总和（计税依据总和）
        BigDecimal totalTaxBasis = valueAddedTaxDetailManager.sumTaxBasisByValueAddedTaxId(entity.getId());
        entity.setCurrentIncome(totalTaxBasis.toString());*/

        em.merge(entity);
    }

    /**
     * 根据作业主体ID和税款所属期生成增值税申报明细数据
     */
    private List<ValueAddedTaxDetailVo> generateValueAddedTaxDetails(Long supplierCorporationId, String taxPaymentPeriod) {
        try {
            // 1. 查询工资明细数据（按身份证分组，取累计收入最大的记录）
            List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailForTaxDeclare(
                    supplierCorporationId, taxPaymentPeriod);

            if (CollectionUtils.isEmpty(salaryDetails)) {
                log.info("未找到作业主体{}税期{}的工资明细数据", supplierCorporationId, taxPaymentPeriod);
                return new ArrayList<>();
            }

            // 2. 查询作业主体配置
            CorporationConfigEntity corporationConfig = corporationManager.queryCorporationConfig(supplierCorporationId);
            if (corporationConfig == null) {
                log.warn("作业主体{}未配置税率信息", supplierCorporationId);
                return new ArrayList<>();
            }

            // 3. 解析附加税配置
            List<SurtaxData> surtaxDataList = parseSurtaxData(corporationConfig.getSurtaxData());

            // 4. 转换为增值税申报明细VO
            List<ValueAddedTaxDetailVo> detailVos = new ArrayList<>();
            for (SalaryDetailEntity salaryDetail : salaryDetails) {
                ValueAddedTaxDetailVo detailVo = convertSalaryDetailToValueAddedTaxDetailVo(
                        salaryDetail, corporationConfig, surtaxDataList, taxPaymentPeriod);
                detailVos.add(detailVo);
            }

            log.info("成功生成{}条增值税申报明细数据", detailVos.size());
            return detailVos;

        } catch (Exception e) {
            log.error("生成增值税申报明细数据失败，作业主体ID: {}, 税期: {}", supplierCorporationId, taxPaymentPeriod, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将工资明细转换为增值税申报明细VO
     */
    private ValueAddedTaxDetailVo convertSalaryDetailToValueAddedTaxDetailVo(
            SalaryDetailEntity salaryDetail,
            CorporationConfigEntity corporationConfig,
            List<SurtaxData> surtaxDataList,
            String taxPaymentPeriod) {

        ValueAddedTaxDetailVo detailVo = new ValueAddedTaxDetailVo();

        // 基本信息
        detailVo.setName(salaryDetail.getName());
        detailVo.setCertificateType(CertificateTypeEnum.ID_CARD.name());
        detailVo.setIdCard(salaryDetail.getIdCard());
        detailVo.setTaxPeriod(taxPaymentPeriod);
        detailVo.setDeclareMonth(Utils.calculateDeclareMonth(taxPaymentPeriod));

        // 计税依据 = 该身份证在指定税期下的应发金额总和
        BigDecimal taxBasis = salaryManager.queryPayableAmountSumByIdCard(
                salaryDetail.getIdCard(),
                salaryDetail.getSupplierCorporationId(),
                taxPaymentPeriod);
        detailVo.setTaxBasis(taxBasis);

        // 增值税税率
        if (corporationConfig.getVatRate() != null) {
            detailVo.setTaxRate(corporationConfig.getVatRate().toString());
        }

        // 增值税金额
        detailVo.setVatAmount(salaryDetail.getCurrentTaxAmount());

        // 设置附加税信息
        setSurtaxInfo(detailVo, salaryDetail, surtaxDataList);

        return detailVo;
    }

    /**
     * 设置附加税信息
     */
    private void setSurtaxInfo(ValueAddedTaxDetailVo detailVo, SalaryDetailEntity salaryDetail, List<SurtaxData> surtaxDataList) {
        if (CollectionUtils.isEmpty(surtaxDataList)) {
            return;
        }

        for (SurtaxData surtaxData : surtaxDataList) {
            if (surtaxData.getSurtaxCode() == SurtaxCodeEnum.URBAN_MAINTENANCE_TAX) {
                // 城市维护建设税
                detailVo.setUrbanTaxRate(surtaxData.getRate().toString());
                detailVo.setUrbanTaxAmount(queryUrbanConstructionTaxSumByIdCard(
                        salaryDetail.getIdCard(),
                        salaryDetail.getSupplierCorporationId(),
                        salaryDetail.getTaxPeriod()));
            } else if (surtaxData.getSurtaxCode() == SurtaxCodeEnum.EDUCATION_SURCHARGE) {
                // 教育费附加
                detailVo.setEduTaxRate(surtaxData.getRate().toString());
                detailVo.setEduTaxAmount(queryEducationSurchargeSumByIdCard(
                        salaryDetail.getIdCard(),
                        salaryDetail.getSupplierCorporationId(),
                        salaryDetail.getTaxPeriod()));
            } else if (surtaxData.getSurtaxCode() == SurtaxCodeEnum.LOCAL_EDUCATION_SURCHARGE) {
                // 地方教育附加
                detailVo.setLocalEduTaxRate(surtaxData.getRate().toString());
                detailVo.setLocalEduTaxAmount(queryLocalEducationSurchargeSumByIdCard(
                        salaryDetail.getIdCard(),
                        salaryDetail.getSupplierCorporationId(),
                        salaryDetail.getTaxPeriod()));
            }
        }
    }

    /**
     * 解析附加税配置JSON
     */
    private List<SurtaxData> parseSurtaxData(String surtaxDataJson) {
        try {
            if (StringUtils.isBlank(surtaxDataJson)) {
                return new ArrayList<>();
            }
            return JSONUtil.toList(surtaxDataJson, SurtaxData.class);
        } catch (Exception e) {
            log.error("解析附加税配置失败: {}", surtaxDataJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据身份证号、作业主体ID和税款所属期查询城市维护建设税总和
     */
    private BigDecimal queryUrbanConstructionTaxSumByIdCard(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.urbanConstructionTax.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    /**
     * 根据身份证号、作业主体ID和税款所属期查询教育费附加总和
     */
    private BigDecimal queryEducationSurchargeSumByIdCard(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.educationSurcharge.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    /**
     * 根据身份证号、作业主体ID和税款所属期查询地方教育附加总和
     */
    private BigDecimal queryLocalEducationSurchargeSumByIdCard(String idCard, Long supplierCorporationId, String taxPeriod) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.localEducationSurcharge.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.taxPeriod.eq(taxPeriod))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

}
